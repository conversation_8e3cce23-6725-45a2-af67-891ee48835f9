Metadata-Version: 2.4
Name: alembic
Version: 1.16.5
Summary: A database migration tool for SQLAlchemy.
Author-email: <PERSON> <<EMAIL>>
License-Expression: MIT
Project-URL: Homepage, https://alembic.sqlalchemy.org
Project-URL: Documentation, https://alembic.sqlalchemy.org/en/latest/
Project-URL: Changelog, https://alembic.sqlalchemy.org/en/latest/changelog.html
Project-URL: Source, https://github.com/sqlalchemy/alembic/
Project-URL: Issue Tracker, https://github.com/sqlalchemy/alembic/issues/
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Environment :: Console
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Database :: Front-Ends
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: SQLAlchemy>=1.4.0
Requires-Dist: Mako
Requires-Dist: typing-extensions>=4.12
Requires-Dist: tomli; python_version < "3.11"
Provides-Extra: tz
Requires-Dist: tzdata; extra == "tz"
Dynamic: license-file

Alembic is a database migrations tool written by the author
of `SQLAlchemy <http://www.sqlalchemy.org>`_.  A migrations tool
offers the following functionality:

* Can emit ALTER statements to a database in order to change
  the structure of tables and other constructs
* Provides a system whereby "migration scripts" may be constructed;
  each script indicates a particular series of steps that can "upgrade" a
  target database to a new version, and optionally a series of steps that can
  "downgrade" similarly, doing the same steps in reverse.
* Allows the scripts to execute in some sequential manner.

The goals of Alembic are:

* Very open ended and transparent configuration and operation.   A new
  Alembic environment is generated from a set of templates which is selected
  among a set of options when setup first occurs. The templates then deposit a
  series of scripts that define fully how database connectivity is established
  and how migration scripts are invoked; the migration scripts themselves are
  generated from a template within that series of scripts. The scripts can
  then be further customized to define exactly how databases will be
  interacted with and what structure new migration files should take.
* Full support for transactional DDL.   The default scripts ensure that all
  migrations occur within a transaction - for those databases which support
  this (Postgresql, Microsoft SQL Server), migrations can be tested with no
  need to manually undo changes upon failure.
* Minimalist script construction.  Basic operations like renaming
  tables/columns, adding/removing columns, changing column attributes can be
  performed through one line commands like alter_column(), rename_table(),
  add_constraint(). There is no need to recreate full SQLAlchemy Table
  structures for simple operations like these - the functions themselves
  generate minimalist schema structures behind the scenes to achieve the given
  DDL sequence.
* "auto generation" of migrations. While real world migrations are far more
  complex than what can be automatically determined, Alembic can still
  eliminate the initial grunt work in generating new migration directives
  from an altered schema.  The ``--autogenerate`` feature will inspect the
  current status of a database using SQLAlchemy's schema inspection
  capabilities, compare it to the current state of the database model as
  specified in Python, and generate a series of "candidate" migrations,
  rendering them into a new migration script as Python directives. The
  developer then edits the new file, adding additional directives and data
  migrations as needed, to produce a finished migration. Table and column
  level changes can be detected, with constraints and indexes to follow as
  well.
* Full support for migrations generated as SQL scripts.   Those of us who
  work in corporate environments know that direct access to DDL commands on a
  production database is a rare privilege, and DBAs want textual SQL scripts.
  Alembic's usage model and commands are oriented towards being able to run a
  series of migrations into a textual output file as easily as it runs them
  directly to a database. Care must be taken in this mode to not invoke other
  operations that rely upon in-memory SELECTs of rows - Alembic tries to
  provide helper constructs like bulk_insert() to help with data-oriented
  operations that are compatible with script-based DDL.
* Non-linear, dependency-graph versioning.   Scripts are given UUID
  identifiers similarly to a DVCS, and the linkage of one script to the next
  is achieved via human-editable markers within the scripts themselves.
  The structure of a set of migration files is considered as a
  directed-acyclic graph, meaning any migration file can be dependent
  on any other arbitrary set of migration files, or none at
  all.  Through this open-ended system, migration files can be organized
  into branches, multiple roots, and mergepoints, without restriction.
  Commands are provided to produce new branches, roots, and merges of
  branches automatically.
* Provide a library of ALTER constructs that can be used by any SQLAlchemy
  application. The DDL constructs build upon SQLAlchemy's own DDLElement base
  and can be used standalone by any application or script.
* At long last, bring SQLite and its inability to ALTER things into the fold,
  but in such a way that SQLite's very special workflow needs are accommodated
  in an explicit way that makes the most of a bad situation, through the
  concept of a "batch" migration, where multiple changes to a table can
  be batched together to form a series of instructions for a single, subsequent
  "move-and-copy" workflow.   You can even use "move-and-copy" workflow for
  other databases, if you want to recreate a table in the background
  on a busy system.

Documentation and status of Alembic is at https://alembic.sqlalchemy.org/

The SQLAlchemy Project
======================

Alembic is part of the `SQLAlchemy Project <https://www.sqlalchemy.org>`_ and
adheres to the same standards and conventions as the core project.

Development / Bug reporting / Pull requests
___________________________________________

Please refer to the
`SQLAlchemy Community Guide <https://www.sqlalchemy.org/develop.html>`_ for
guidelines on coding and participating in this project.

Code of Conduct
_______________

Above all, SQLAlchemy places great emphasis on polite, thoughtful, and
constructive communication between users and developers.
Please see our current Code of Conduct at
`Code of Conduct <https://www.sqlalchemy.org/codeofconduct.html>`_.

License
=======

Alembic is distributed under the `MIT license
<https://opensource.org/licenses/MIT>`_.
