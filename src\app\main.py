from fastapi import FastAP<PERSON>

from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware

from .api.routers.v1.health import router as health_router
from .api.routers.v1.auth import router as auth_router
from .api.routers.api_router import api_router

# Create database tables
# Base.metadata.create_all(bind=engine)



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("Starting Smart Track API...")
    yield
    # Shutdown
    print("Shutting down Smart Track API...")


# Initialize FastAPI app
app = FastAPI(
    title="Smart Track API",
    description="Sales Force Automation API for Smart Track Mobile Application",
    version="1.0.0",
    lifespan=lifespan
)

app.include_router(api_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

