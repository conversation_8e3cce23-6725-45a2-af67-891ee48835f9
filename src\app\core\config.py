from __future__ import annotations

import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
from urllib.parse import quote_plus


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=True,
        extra="ignore"  # Ignore extra environment variables
    )

    APP_NAME: str = "Smart Track Backend"
    ENV: str = "development"

    # Database
    DATABASE_URL: Optional[str] = None  # e.g., postgresql+asyncpg://user:pass@host:5432/smart_track

    # JWT
    JWT_SECRET_KEY: Optional[str] = None
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Infobip
    INFOBIP_BASE_URL: Optional[str] = None
    INFOBIP_API_KEY: Optional[str] = None
    INFOBIP_APPLICATION_ID: Optional[str] = None
    INFOBIP_MESSAGE_ID: Optional[str] = None
    INFOBIP_SENDER_ID: Optional[str] = None

    # SAP
    SAP_BASE_URL: Optional[str] = None
    SAP_API_KEY: Optional[str] = None

    @property
    def DATABASE_URL(self) -> str:
        """Construct the database URL from individual components"""
        password = quote_plus(self.DATABASE_PASSWORD)
        return f"postgresql+asyncpg://{self.DATABASE_USERNAME}:{password}@{self.DATABASE_HOSTNAME}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"

# Initialize settings
settings = Settings()
